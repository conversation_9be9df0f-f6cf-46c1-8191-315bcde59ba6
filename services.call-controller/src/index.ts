import { createApp } from '@/app';
import { config } from '@/config';
import { getAppContext } from '@/core/context';
import { disconnectDatabase } from '@/core/database';
import { getLogger } from '@/core/logger';

// Suppress the KafkaJS negative timeout warning which is a known issue
// This warning doesn't affect functionality but is noisy in logs
process.removeAllListeners('warning');
process.on('warning', (warning) => {
  // Filter out the specific TimeoutNegativeWarning from KafkaJS
  if (warning.name === 'TimeoutNegativeWarning') {
    return; // Suppress this warning
  }
  // Log other warnings normally
  console.warn(warning.name, warning.message);
});

const appLogger = getLogger('Main');

/**
 * Main application startup function.
 * Handles initialization, startup, and graceful shutdown.
 */
async function main(): Promise<void> {
  appLogger.info('Starting Call Control Service', {
    service: config.serviceName,
    port: config.port,
    environment: config.nodeEnv,
  });

  let server: any = null;

  try {
    // Initialize application context
    const appContext = await getAppContext();
    await appContext.initialize();

    appLogger.info('Application context initialized successfully');

    // Create Express application
    const app = createApp();

    // Start HTTP server
    server = app.listen(config.port, config.host, () => {
      appLogger.info(`Call Control service started successfully`, {
        host: config.host,
        port: config.port,
        url: `http://${config.host}:${config.port}`,
      });
    });

    // Setup graceful shutdown handlers
    const gracefulShutdown = async (signal: string) => {
      appLogger.info(`Received ${signal}, starting graceful shutdown...`);

      // Stop accepting new connections
      if (server) {
        server.close(async () => {
          appLogger.info('HTTP server closed');

          try {
            // Clean up application context
            const appContext = await getAppContext();
            await appContext.cleanup();

            // Disconnect from database
            await disconnectDatabase();

            appLogger.info('Call Control service shutdown complete');
            process.exit(0);

          } catch (error) {
            appLogger.error('Error during graceful shutdown:', error);
            process.exit(1);
          }
        });
      } else {
        process.exit(0);
      }

      // Force exit after 30 seconds if graceful shutdown takes too long
      setTimeout(() => {
        appLogger.error('Graceful shutdown timed out, forcing exit');
        process.exit(1);
      }, 30000);
    };

    // Handle shutdown signals
    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));

    // Handle uncaught exceptions and unhandled rejections
    process.on('uncaughtException', (error) => {
      appLogger.error('Uncaught exception:', error);
      gracefulShutdown('uncaughtException');
    });

    process.on('unhandledRejection', (reason, promise) => {
      appLogger.error('Unhandled rejection at:', promise, 'reason:', reason);
      gracefulShutdown('unhandledRejection');
    });

  } catch (error) {
    appLogger.error('Fatal error during startup:', error);
    
    // Attempt cleanup if server was started
    if (server) {
      server.close();
    }

    try {
      await disconnectDatabase();
    } catch (dbError) {
      appLogger.error('Error disconnecting from database during startup failure:', dbError);
    }

    process.exit(1);
  }
}

// Start the application
main().catch((error) => {
  console.error('Fatal error during startup:', error);
  process.exit(1);
});
